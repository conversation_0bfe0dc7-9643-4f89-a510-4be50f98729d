# Java 24 Compact Object Headers Configuration
# This file contains JVM arguments to enable compact object headers feature

# Enable experimental features
-XX:+UnlockExperimentalVMOptions

# Enable compact object headers (Java 24 feature)
-XX:+UseCompactObjectHeaders

# Optional: Print final flags to verify configuration
-XX:+PrintFlagsFinal

# Optional: Enable GC logging to monitor memory improvements
-Xlog:gc*:gc.log:time

# Optional: Print memory usage statistics
-XX:+PrintGCDetails

# Memory settings (adjust based on your application needs)
-Xms512m
-Xmx2g

# Note: Compact object headers require compressed class pointers to be enabled (default)
# If you need to disable compressed class pointers for any reason, compact headers will be disabled
# -XX:-UseCompressedClassPointers  # DO NOT use this with compact headers
