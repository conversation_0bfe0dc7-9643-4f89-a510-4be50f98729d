#!/bin/bash

# <PERSON><PERSON>t to run the desktop application with Java 24 Compact Object Headers enabled

echo "Starting application with Java 24 Compact Object Headers..."
echo "Make sure you have Java 24 installed and set as JAVA_HOME"

# Check Java version
java -version

echo ""
echo "Running Gradle desktop application with compact object headers..."

# Run the desktop application with compact object headers enabled
./gradlew :composeApp:runDistributable \
    -Dorg.gradle.jvmargs="-XX:+UnlockExperimentalVMOptions -XX:+UseCompactObjectHeaders -XX:+PrintFlagsFinal"

echo "Application finished."
